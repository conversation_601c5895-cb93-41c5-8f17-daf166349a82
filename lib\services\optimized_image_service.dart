import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

/// Optimized image service for WhatsApp-like performance
class OptimizedImageService {
  static final OptimizedImageService _instance =
      OptimizedImageService._internal();
  factory OptimizedImageService() => _instance;
  OptimizedImageService._internal();

  /// Compress multiple images concurrently for maximum speed
  static Future<List<File>> compressImagesInParallel(
    List<XFile> images, {
    int quality = 75, // Optimized quality for speed vs size balance
    int maxWidth = 1024,
    int maxHeight = 1024,
    Function(int processed, int total)? onProgress,
  }) async {
    if (images.isEmpty) return [];

    final List<File> compressedImages = [];
    int processedCount = 0;

    // Process images in optimized batches for faster compression
    const batchSize = 12; // Reduced batch size for better memory management

    for (int i = 0; i < images.length; i += batchSize) {
      final batch = images.skip(i).take(batchSize).toList();

      // Process batch concurrently
      final futures = batch.map(
        (image) => _compressSingleImage(
          image,
          quality: quality,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        ),
      );

      final batchResults = await Future.wait(futures);

      // Add successful compressions to results
      for (final result in batchResults) {
        if (result != null) {
          compressedImages.add(result);
        }
        processedCount++;
        onProgress?.call(processedCount, images.length);
      }

      // Small delay between batches to prevent system overload
      if (i + batchSize < images.length) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    return compressedImages;
  }

  /// Compress a single image with optimized settings for speed
  static Future<File?> _compressSingleImage(
    XFile image, {
    int quality = 75, // Slightly higher quality for faster processing
    int maxWidth = 1024,
    int maxHeight = 1024,
  }) async {
    try {
      final dir = Directory.systemTemp;
      final targetPath = path.join(
        dir.path,
        '${DateTime.now().millisecondsSinceEpoch}_${path.basename(image.path)}',
      );

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        image.path,
        targetPath,
        quality: quality,
        minWidth: maxWidth,
        minHeight: maxHeight,
        format: CompressFormat.jpeg,
        keepExif: false, // Remove EXIF data to reduce file size
      );

      return compressedFile != null ? File(compressedFile.path) : null;
    } catch (e) {
      // print('Error compressing image ${image.path}: $e');
      // Return original file if compression fails
      return File(image.path);
    }
  }

  /// Get optimized image picker settings for better performance
  static Future<List<XFile>?> pickMultipleImages({
    int imageQuality = 80,
    int maxImages = 100, // WhatsApp-like limit
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile>? images = await picker.pickMultiImage(
        imageQuality: imageQuality,
        maxWidth: 1920, // Higher initial quality for better preview
        maxHeight: 1920,
      );

      if (images != null && images.length > maxImages) {
        // Limit to maximum images
        return images.take(maxImages).toList();
      }

      return images;
    } catch (e) {
      // print('Error picking images: $e');
      return null;
    }
  }

  /// Validate and filter images
  static Future<List<XFile>> validateImages(List<XFile> images) async {
    final validImages = <XFile>[];
    const maxFileSize = 50 * 1024 * 1024; // 50MB limit per image

    for (final image in images) {
      try {
        final file = File(image.path);
        final fileSize = await file.length();

        if (fileSize <= maxFileSize) {
          validImages.add(image);
        } else {
          // print('Image ${image.name} is too large: ${fileSize} bytes');
        }
      } catch (e) {
        // print('Error validating image ${image.path}: $e');
      }
    }

    return validImages;
  }

  /// Generate thumbnail for quick preview
  static Future<Uint8List?> generateThumbnail(
    String imagePath, {
    int width = 200,
    int height = 200,
  }) async {
    try {
      final result = await FlutterImageCompress.compressWithFile(
        imagePath,
        minWidth: width,
        minHeight: height,
        quality: 60,
        format: CompressFormat.jpeg,
      );
      return result;
    } catch (e) {
      // print('Error generating thumbnail for $imagePath: $e');
      return null;
    }
  }

  /// Calculate total size of images
  static Future<int> calculateTotalSize(List<XFile> images) async {
    int totalSize = 0;

    for (final image in images) {
      try {
        final file = File(image.path);
        final size = await file.length();
        totalSize += size;
      } catch (e) {
        // print('Error calculating size for ${image.path}: $e');
      }
    }

    return totalSize;
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Batch process images for sending
  static Future<List<ProcessedImage>> batchProcessImages(
    List<XFile> images, {
    Function(int processed, int total)? onProgress,
  }) async {
    final List<ProcessedImage> processedImages = [];

    // First, validate all images
    final validImages = await validateImages(images);

    if (validImages.isEmpty) {
      return processedImages;
    }

    // Compress images in parallel
    final compressedFiles = await compressImagesInParallel(
      validImages,
      onProgress: onProgress,
    );

    // Create processed image objects
    for (int i = 0; i < compressedFiles.length; i++) {
      final compressedFile = compressedFiles[i];
      final originalImage = validImages[i];

      try {
        final fileSize = await compressedFile.length();
        final thumbnail = await generateThumbnail(compressedFile.path);

        processedImages.add(
          ProcessedImage(
            originalPath: originalImage.path,
            compressedFile: compressedFile,
            fileSize: fileSize,
            thumbnail: thumbnail,
            name: originalImage.name,
          ),
        );
      } catch (e) {
        // print('Error processing image ${originalImage.path}: $e');
      }
    }

    return processedImages;
  }
}

/// Processed image data structure
class ProcessedImage {
  final String originalPath;
  final File compressedFile;
  final int fileSize;
  final Uint8List? thumbnail;
  final String name;

  ProcessedImage({
    required this.originalPath,
    required this.compressedFile,
    required this.fileSize,
    this.thumbnail,
    required this.name,
  });

  String get formattedSize => OptimizedImageService.formatFileSize(fileSize);
}

/// Image processing status
enum ImageProcessingStatus {
  idle,
  picking,
  validating,
  compressing,
  ready,
  sending,
  completed,
  error,
}

/// Image processing state
class ImageProcessingState {
  final ImageProcessingStatus status;
  final List<ProcessedImage> processedImages;
  final int processedCount;
  final int totalCount;
  final String? errorMessage;

  const ImageProcessingState({
    this.status = ImageProcessingStatus.idle,
    this.processedImages = const [],
    this.processedCount = 0,
    this.totalCount = 0,
    this.errorMessage,
  });

  ImageProcessingState copyWith({
    ImageProcessingStatus? status,
    List<ProcessedImage>? processedImages,
    int? processedCount,
    int? totalCount,
    String? errorMessage,
  }) {
    return ImageProcessingState(
      status: status ?? this.status,
      processedImages: processedImages ?? this.processedImages,
      processedCount: processedCount ?? this.processedCount,
      totalCount: totalCount ?? this.totalCount,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  double get progress {
    if (totalCount == 0) return 0.0;
    return processedCount / totalCount;
  }

  bool get isProcessing =>
      status == ImageProcessingStatus.picking ||
      status == ImageProcessingStatus.validating ||
      status == ImageProcessingStatus.compressing;

  bool get isReady => status == ImageProcessingStatus.ready;
  bool get isSending => status == ImageProcessingStatus.sending;
  bool get hasError => status == ImageProcessingStatus.error;
}
